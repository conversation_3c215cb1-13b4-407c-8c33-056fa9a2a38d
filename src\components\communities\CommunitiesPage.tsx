"use client";

import { useState } from "react";
import {
  Box,
  Button,
  Heading,
  Text,
  Flex,
  Input,
  SimpleGrid,
} from "@chakra-ui/react";
import { InputGroup } from "@/components/ui/input-group";
import { Ta<PERSON>, <PERSON>b<PERSON>ist, Tab, <PERSON>b<PERSON>ane<PERSON>, TabPanel } from "@/components/ui/tabs";
import { useTranslation } from "react-i18next";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Search, Plus } from "lucide-react";
import CommunityCard from "@/components/community/CommunityCard";

interface CommunitiesPageProps {
  userCommunities: any[];
  publicCommunities: any[];
  language: string;
}

export default function CommunitiesPage({
  userCommunities = [],
  publicCommunities = [],
  language = "en",
}: CommunitiesPageProps) {
  const { direction } = useDirection();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Temporarily bypass translation issues
  const getText = (key: string, fallback: string) => {
    return fallback;
  };

  const filteredPublicCommunities = publicCommunities.filter((community) =>
    community.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    community.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredUserCommunities = userCommunities.filter((community) =>
    community.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    community.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Box dir={direction}>
      {/* Header */}
      <Flex justify="space-between" align="center" mb={6}>
        <Box>
          <Heading as="h1" size="xl" mb={2}>
            {getText("communities.title", "Communities")}
          </Heading>
          <Text color="gray.600">
            {getText("communities.description", "Discover and join communities")}
          </Text>
        </Box>
        <Button
          colorPalette="teal"
          onClick={() => setIsModalOpen(true)}
        >
          <Plus size={16} />
          {getText("community.create", "Create Community")}
        </Button>
      </Flex>

      {/* Search */}
      <Box mb={6}>
        <InputGroup
          maxW="400px"
          startElement={<Search size={18} color="gray.400" />}
        >
          <Input
            placeholder={getText("communities.searchPlaceholder", "Search communities...")}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </InputGroup>
      </Box>

      {/* Tabs */}
      <Tabs defaultValue="my-communities">
        <TabList mb={6}>
          <Tab value="my-communities">
            {getText("communities.myCommunities", "My Communities")} ({filteredUserCommunities.length})
          </Tab>
          <Tab value="discover">
            {getText("communities.discover", "Discover")} ({filteredPublicCommunities.length})
          </Tab>
        </TabList>

        <TabPanels>
          {/* My Communities Tab */}
          <TabPanel value="my-communities" px={0}>
            {filteredUserCommunities.length > 0 ? (
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                {filteredUserCommunities.map((community) => (
                  <CommunityCard
                    key={community.id}
                    id={community.id}
                    name={community.name}
                    description={community.description}
                    thumbnailUrl={community.banner_url}
                    isPrivate={community.is_private}
                    language={language as "en" | "ar"}
                    memberCount={community.member_count || 0}
                  />
                ))}
              </SimpleGrid>
            ) : (
              <Box textAlign="center" py={12}>
                <Text color="gray.500" mb={4}>
                  {searchQuery
                    ? getText("communities.noSearchResults", "No communities found matching your search")
                    : getText("communities.noUserCommunities", "You haven't joined any communities yet")
                  }
                </Text>
                {!searchQuery && (
                  <Button
                    colorPalette="teal"
                    variant="outline"
                    onClick={() => setIsModalOpen(true)}
                  >
                    {getText("community.create", "Create Community")}
                  </Button>
                )}
              </Box>
            )}
          </TabPanel>

          {/* Discover Tab */}
          <TabPanel value="discover" px={0}>
            {filteredPublicCommunities.length > 0 ? (
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                {filteredPublicCommunities.map((community) => (
                  <CommunityCard
                    key={community.id}
                    id={community.id}
                    name={community.name}
                    description={community.description}
                    thumbnailUrl={community.banner_url}
                    isPrivate={community.is_private}
                    language={language as "en" | "ar"}
                    memberCount={community.member_count || 0}
                  />
                ))}
              </SimpleGrid>
            ) : (
              <Box textAlign="center" py={12}>
                <Text color="gray.500">
                  {searchQuery
                    ? getText("communities.noSearchResults", "No communities found matching your search")
                    : getText("communities.noPublicCommunities", "No public communities available")
                  }
                </Text>
              </Box>
            )}
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
}
