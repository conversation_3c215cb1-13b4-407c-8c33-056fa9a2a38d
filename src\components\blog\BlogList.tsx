import React from "react";
import { Avatar } from "../ui/avatar";
import { Card } from "../ui/card";
import { Badge } from "@chakra-ui/react";
import { MessageSquare, ThumbsUp } from "lucide-react";

interface Author {
  full_name: string;
  avatar_url: string;
}

interface Content {
  text: string;
  image?: string;
}

interface Reaction {
  count: number;
}

interface Comment {
  count: number;
}

interface BlogPost {
  id: string;
  title: string;
  content: Content;
  author: Author;
  created_at: string;
  author_id: string;
  status?: string;
  reactions?: Reaction[];
  comments?: Comment[];
}

interface BlogListProps {
  posts: BlogPost[];
  currentUser?: {
    id: string;
    full_name: string;
    avatar_url: string;
  };
}

export default function BlogList({ posts, currentUser }: BlogListProps) {
  return (
    <div className="space-y-6 bg-white">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Blog Posts</h1>
        {currentUser && (
          <button className="px-4 py-2 text-white bg-teal-600 rounded-md hover:bg-teal-700">
            Create New Post
          </button>
        )}
      </div>

      <div className="space-y-6">
        {posts.map((post) => (
          <Card key={post.id} className="overflow-hidden">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <Avatar>
                  <img
                    src={post.author.avatar_url}
                    alt={post.author.full_name}
                    className="object-cover w-full h-full"
                  />
                </Avatar>
                <div>
                  <p className="font-medium">{post.author.full_name}</p>
                  <p className="text-sm text-gray-500">
                    {new Date(post.created_at).toLocaleDateString()}
                  </p>
                </div>
                {post.status && (
                  <Badge variant="outline" className="ml-auto">
                    {post.status}
                  </Badge>
                )}
              </div>

              <h2 className="mb-3 text-xl font-bold">{post.title}</h2>
              <p className="mb-4 text-gray-700">{post.content.text}</p>

              {post.content.image && (
                <div className="mb-4 overflow-hidden rounded-md">
                  <img
                    src={post.content.image}
                    alt={post.title}
                    className="object-cover w-full h-64"
                  />
                </div>
              )}

              <div className="flex items-center gap-4 pt-4 mt-4 border-t">
                <div className="flex items-center gap-1">
                  <ThumbsUp className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-500">
                    {post.reactions?.[0]?.count || 0}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <MessageSquare className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-500">
                    {post.comments?.[0]?.count || 0}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
