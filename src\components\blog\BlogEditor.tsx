"use client";

import { useState, useEffect } from "react";
import {
  Box,
  Button,
  Input,
  Textarea,
  Switch,
  Flex,
  Heading,
  Text,
  Container,
  VStack,
  HStack,
  Select,
  Image,
  IconButton,
  Badge,
  CloseButton,
} from "@chakra-ui/react";
import { useColorModeValue } from "@/components/ui/color-mode";
import { Field } from "@/components/ui/field";
import { toaster } from "@/components/ui/toaster";
import { InputGroup } from "@/components/ui/input-group";
import { Tag } from "@/components/ui/tag";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase/client";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Plus, X, Upload, Eye } from "lucide-react";

interface BlogEditorProps {
  spaceId: string;
  currentUser: any;
  post?: any; // For editing existing post
  categories?: string[];
}

export default function BlogEditor({
  spaceId,
  currentUser,
  post,
  categories = [
    "Technology",
    "Design",
    "Business",
    "Lifestyle",
    "Health",
    "Education",
  ],
}: BlogEditorProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { direction } = useDirection();

  const [title, setTitle] = useState(post?.title || "");
  const [content, setContent] = useState(post?.content?.text || "");
  const [coverImage, setCoverImage] = useState(post?.content?.image || "");
  const [isFeatured, setIsFeatured] = useState(
    post?.status === "featured" || false,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [previewMode, setPreviewMode] = useState(false);

  // Tags handling
  const [tags, setTags] = useState<string[]>(post?.metadata?.tags || []);
  const [tagInput, setTagInput] = useState("");

  // Category
  const [category, setCategory] = useState(post?.metadata?.category || "");

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    if (!title.trim()) {
      newErrors.title = t("blog.titleRequired", "Title is required");
    }
    if (!content.trim()) {
      newErrors.content = t("blog.contentRequired", "Content is required");
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && tagInput) {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const postData = {
        title,
        content: {
          text: content,
          image: coverImage,
        },
        type: "blog",
        language: currentUser.language || "en",
        status: isFeatured ? "featured" : "published",
        metadata: {
          tags,
          category,
          seo: {
            description: content.substring(0, 160),
            keywords: tags.join(", "),
          },
        },
      };

      if (post) {
        // Update existing post
        const { error } = await supabase
          .from("content_modules")
          .update(postData)
          .eq("id", post.id);

        if (error) throw error;

        toaster.success({
          title: t("blog.updateSuccess", "Blog post updated"),
          duration: 3000,
        });

        router.push(`/spaces/${spaceId}/posts/${post.id}`);
      } else {
        // Create new post
        const { data, error } = await supabase
          .from("content_modules")
          .insert([
            {
              ...postData,
              space_id: spaceId,
              author_id: currentUser.id,
            },
          ])
          .select();

        if (error) throw error;

        toaster.success({
          title: t("blog.createSuccess", "Blog post created"),
          duration: 3000,
        });

        if (data && data[0]) {
          router.push(`/spaces/${spaceId}/posts/${data[0].id}`);
        } else {
          router.push(`/spaces/${spaceId}`);
        }
      }

      router.refresh();
    } catch (error: any) {
      toaster.error({
        title: post
          ? t("blog.updateError", "Error updating blog post")
          : t("blog.createError", "Error creating blog post"),
        description: error.message,
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box dir={direction}>
      <Container maxW="container.xl" py={6}>
        <Flex justify="space-between" align="center" mb={6}>
          <Heading as="h1" size="xl">
            {post
              ? t("blog.editPost", "Edit Blog Post")
              : t("blog.createPost", "Create New Blog Post")}
          </Heading>
          <HStack>
            <Button
              variant="outline"
              onClick={() => setPreviewMode(!previewMode)}
            >
              <Eye size={16} />
              {previewMode
                ? t("blog.editMode", "Edit Mode")
                : t("blog.preview", "Preview")}
            </Button>
            <Button
              colorPalette="teal"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {post ? t("blog.update", "Update") : t("blog.publish", "Publish")}
            </Button>
          </HStack>
        </Flex>

        {previewMode ? (
          <Box
            bg={bgColor}
            borderRadius="xl"
            borderWidth="1px"
            borderColor={borderColor}
            overflow="hidden"
          >
            {coverImage && (
              <Box height="400px" overflow="hidden">
                <Image
                  src={coverImage}
                  alt={title}
                  objectFit="cover"
                  width="100%"
                  height="100%"
                />
              </Box>
            )}
            <Box p={8}>
              {category && (
                <Badge
                  colorPalette="teal"
                  fontSize="sm"
                  px={3}
                  py={1}
                  borderRadius="full"
                  mb={4}
                >
                  {category}
                </Badge>
              )}
              <Heading as="h1" size="2xl" mb={6}>
                {title || t("blog.untitledPost", "Untitled Post")}
              </Heading>
              <Flex align="center" gap={3} mb={8}>
                <Text fontWeight="medium">{currentUser?.full_name}</Text>
              </Flex>
              {tags.length > 0 && (
                <HStack gap={2} mb={8} flexWrap="wrap">
                  {tags.map((tag, index) => (
                    <Tag
                      key={index}
                      size="md"
                      borderRadius="full"
                      variant="subtle"
                      colorPalette="teal"
                    >
                      {tag}
                    </Tag>
                  ))}
                </HStack>
              )}
              <Text whiteSpace="pre-wrap" mb={8} fontSize="lg" lineHeight="1.8">
                {content ||
                  t(
                    "blog.noContent",
                    "No content yet. Start writing in edit mode.",
                  )}
              </Text>
            </Box>
          </Box>
        ) : (
          <VStack gap={6} align="stretch">
            <Field
              label={t("blog.title", "Title")}
              invalid={!!errors.title}
              errorText={errors.title}
            >
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder={t(
                  "blog.titlePlaceholder",
                  "Enter blog post title",
                )}
                size="lg"
                fontSize="xl"
              />
            </Field>

            <Field label={t("blog.coverImage", "Cover Image URL")}>
              <Input
                value={coverImage}
                onChange={(e) => setCoverImage(e.target.value)}
                placeholder={t("blog.coverImagePlaceholder", "Enter image URL")}
              />
              {coverImage && (
                <Box
                  mt={2}
                  position="relative"
                  height="200px"
                  overflow="hidden"
                  borderRadius="md"
                >
                  <Image
                    src={coverImage}
                    alt="Cover preview"
                    objectFit="cover"
                    width="100%"
                    height="100%"
                  />
                  <IconButton
                    aria-label="Remove cover image"
                    size="sm"
                    position="absolute"
                    top={2}
                    right={2}
                    onClick={() => setCoverImage("")}
                  >
                    <X size={16} />
                  </IconButton>
                </Box>
              )}
            </Field>

            <Flex gap={6} direction={{ base: "column", md: "row" }}>
              <Field label={t("blog.category", "Category")} flex={1}>
                <select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  style={{
                    width: "100%",
                    padding: "8px 12px",
                    border: "1px solid #e2e8f0",
                    borderRadius: "6px",
                    backgroundColor: "white",
                    fontSize: "14px",
                    outline: "none",
                  }}
                >
                  <option value="">{t("blog.selectCategory", "Select category")}</option>
                  {categories.map((cat) => (
                    <option key={cat} value={cat}>
                      {cat}
                    </option>
                  ))}
                </select>
              </Field>

              <Field label={t("blog.tags", "Tags")} flex={1}>
                <InputGroup
                  endElement={
                    <Button
                      size="sm"
                      onClick={handleAddTag}
                      disabled={!tagInput.trim()}
                    >
                      <Plus size={16} />
                    </Button>
                  }
                >
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder={t(
                      "blog.tagsPlaceholder",
                      "Add a tag and press Enter",
                    )}
                    onKeyDown={handleKeyDown}
                  />
                </InputGroup>
                {tags.length > 0 && (
                  <HStack gap={2} mt={2} flexWrap="wrap">
                    {tags.map((tag, index) => (
                      <Tag
                        key={index}
                        size="md"
                        borderRadius="full"
                        variant="subtle"
                        colorPalette="teal"
                        closable
                        onClose={() => handleRemoveTag(tag)}
                      >
                        {tag}
                      </Tag>
                    ))}
                  </HStack>
                )}
              </Field>
            </Flex>

            <Field
              label={t("blog.content", "Content")}
              invalid={!!errors.content}
              errorText={errors.content}
            >
              <Textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={t(
                  "blog.contentPlaceholder",
                  "Write your blog post content here...",
                )}
                minH="400px"
                size="lg"
              />
            </Field>

            <Field label={t("blog.featured", "Featured Post")}>
              <input
                type="checkbox"
                checked={isFeatured}
                onChange={(e) => setIsFeatured(e.target.checked)}
                style={{
                  width: "20px",
                  height: "20px",
                  accentColor: "#319795",
                }}
              />
            </Field>
          </VStack>
        )}
      </Container>
    </Box>
  );
}
