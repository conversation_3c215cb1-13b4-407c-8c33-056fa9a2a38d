import { Select as ChakraSelect } from "@chakra-ui/react"
import * as React from "react"

export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  children?: React.ReactNode
  placeholder?: string
  width?: any
  size?: string
}

export const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  function Select(props, ref) {
    const { placeholder, children, width, size, ...rest } = props
    return (
      <ChakraSelect ref={ref} width={width} size={size} {...rest}>
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {children}
      </ChakraSelect>
    )
  },
)

Select.displayName = "Select"
