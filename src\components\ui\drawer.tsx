"use client"

import { Dialog } from "@chakra-ui/react"
import { forwardRef } from "react"

export interface DrawerProps extends Omit<Dialog.RootProps, 'open' | 'onOpenChange'> {
  placement?: "left" | "right" | "top" | "bottom"
  isOpen?: boolean
  onClose?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export const Drawer = forwardRef<HTMLDivElement, DrawerProps>(
  function Drawer({ placement = "right", isOpen, onClose, open, onOpenChange, ...props }, ref) {
    // Support both old and new API
    const isDrawerOpen = open !== undefined ? open : isOpen
    const handleOpenChange = onOpenChange || ((open: boolean) => {
      if (!open && onClose) {
        onClose()
      }
    })

    return (
      <Dialog.Root
        ref={ref}
        open={isDrawerOpen}
        onOpenChange={handleOpenChange}
        {...props}
      />
    )
  },
)

export interface DrawerOverlayProps extends Dialog.BackdropProps {}

export const DrawerOverlay = forwardRef<HTMLDivElement, DrawerOverlayProps>(
  function DrawerOverlay(props, ref) {
    return <Dialog.Backdrop ref={ref} {...props} />
  },
)

export interface DrawerContentProps extends Dialog.ContentProps {}

export const DrawerContent = forwardRef<HTMLDivElement, DrawerContentProps>(
  function DrawerContent(props, ref) {
    return <Dialog.Content ref={ref} {...props} />
  },
)

export interface DrawerHeaderProps extends Dialog.HeaderProps {}

export const DrawerHeader = forwardRef<HTMLDivElement, DrawerHeaderProps>(
  function DrawerHeader(props, ref) {
    return <Dialog.Header ref={ref} {...props} />
  },
)

export interface DrawerCloseButtonProps extends Dialog.CloseTriggerProps {}

export const DrawerCloseButton = forwardRef<HTMLButtonElement, DrawerCloseButtonProps>(
  function DrawerCloseButton(props, ref) {
    return <Dialog.CloseTrigger ref={ref} {...props} />
  },
)

export interface DrawerBodyProps extends Dialog.BodyProps {}

export const DrawerBody = forwardRef<HTMLDivElement, DrawerBodyProps>(
  function DrawerBody(props, ref) {
    return <Dialog.Body ref={ref} {...props} />
  },
)

export interface DrawerFooterProps extends Dialog.FooterProps {}

export const DrawerFooter = forwardRef<HTMLDivElement, DrawerFooterProps>(
  function DrawerFooter(props, ref) {
    return <Dialog.Footer ref={ref} {...props} />
  },
)
